# Femboy Bootloader Makefile (C Version)

# Tools
ASM = nasm
CC = gcc
LD = ld
DD = dd
QEMU = qemu-system-x86_64

# Flags
ASMFLAGS = -f elf
CFLAGS = -m16 -march=i386 -ffreestanding -fno-stack-protector -fno-builtin -nostdlib -Os -fno-pic -fno-pie
STAGE2_LDFLAGS = -m elf_i386 -T stage2.ld --oformat=binary
KERNEL_LDFLAGS = -m elf_i386 -T kernel.ld --oformat=binary

# Files
STAGE1_SRC = stage1.asm
STAGE1_BIN = stage1.bin
STAGE2_C_SRC = stage2.c
STAGE2_ASM_SRC = stage2_asm.asm
STAGE2_BIN = stage2.bin
SIMPLE_KERNEL_C_SRC = simple_kernel.c
SIMPLE_KERNEL_ASM_SRC = simple_kernel_asm.asm
SIMPLE_KERNEL_BIN = simple_kernel.bin
EXAMPLE_KERNEL_C_SRC = example_kernel.c
EXAMPLE_KERNEL_ASM_SRC = example_kernel_asm.asm
EXAMPLE_KERNEL_BIN = example_kernel.bin
DISK_IMG = boot.img

# Default target
all: $(DISK_IMG)

# Build Stage 1 (still assembly)
$(STAGE1_BIN): $(STAGE1_SRC)
	@echo "Assembling Stage 1..."
	$(ASM) -f bin $(STAGE1_SRC) -o $(STAGE1_BIN)

# Build Stage 2 (C + Assembly)
stage2_asm.o: $(STAGE2_ASM_SRC)
	@echo "Assembling Stage 2 stub..."
	$(ASM) $(ASMFLAGS) $(STAGE2_ASM_SRC) -o stage2_asm.o

stage2.o: $(STAGE2_C_SRC)
	@echo "Compiling Stage 2 C code..."
	$(CC) $(CFLAGS) -c $(STAGE2_C_SRC) -o stage2.o

$(STAGE2_BIN): stage2_asm.o stage2.o
	@echo "Linking Stage 2..."
	$(LD) $(STAGE2_LDFLAGS) stage2_asm.o stage2.o -o $(STAGE2_BIN)

# Build Simple Kernel (C + Assembly)
simple_kernel_asm.o: $(SIMPLE_KERNEL_ASM_SRC)
	@echo "Assembling Simple Kernel stub..."
	$(ASM) $(ASMFLAGS) $(SIMPLE_KERNEL_ASM_SRC) -o simple_kernel_asm.o

simple_kernel.o: $(SIMPLE_KERNEL_C_SRC)
	@echo "Compiling Simple Kernel C code..."
	$(CC) $(CFLAGS) -c $(SIMPLE_KERNEL_C_SRC) -o simple_kernel.o

$(SIMPLE_KERNEL_BIN): simple_kernel_asm.o simple_kernel.o
	@echo "Linking Simple Kernel..."
	$(LD) $(KERNEL_LDFLAGS) simple_kernel_asm.o simple_kernel.o -o $(SIMPLE_KERNEL_BIN).tmp
	@echo "Padding Simple Kernel to 8 sectors..."
	cp $(SIMPLE_KERNEL_BIN).tmp $(SIMPLE_KERNEL_BIN)
	truncate -s 4096 $(SIMPLE_KERNEL_BIN)
	@rm -f $(SIMPLE_KERNEL_BIN).tmp

# Build Example Kernel (C + Assembly)
example_kernel_asm.o: $(EXAMPLE_KERNEL_ASM_SRC)
	@echo "Assembling Example Kernel stub..."
	$(ASM) $(ASMFLAGS) $(EXAMPLE_KERNEL_ASM_SRC) -o example_kernel_asm.o

example_kernel.o: $(EXAMPLE_KERNEL_C_SRC)
	@echo "Compiling Example Kernel C code..."
	$(CC) $(CFLAGS) -c $(EXAMPLE_KERNEL_C_SRC) -o example_kernel.o

$(EXAMPLE_KERNEL_BIN): example_kernel_asm.o example_kernel.o
	@echo "Linking Example Kernel..."
	$(LD) $(KERNEL_LDFLAGS) example_kernel_asm.o example_kernel.o -o $(EXAMPLE_KERNEL_BIN).tmp
	@echo "Padding Example Kernel to 8 sectors..."
	cp $(EXAMPLE_KERNEL_BIN).tmp $(EXAMPLE_KERNEL_BIN)
	truncate -s 4096 $(EXAMPLE_KERNEL_BIN)
	@rm -f $(EXAMPLE_KERNEL_BIN).tmp

# Create disk image with simple kernel (default)
$(DISK_IMG): $(STAGE1_BIN) $(STAGE2_BIN) $(SIMPLE_KERNEL_BIN)
	@echo "Creating disk image with Simple Kernel..."
	$(DD) if=/dev/zero of=$(DISK_IMG) bs=512 count=2880 2>/dev/null
	@echo "Writing Stage 1 to boot sector..."
	$(DD) if=$(STAGE1_BIN) of=$(DISK_IMG) bs=512 conv=notrunc 2>/dev/null
	@echo "Writing Stage 2 starting at sector 2..."
	$(DD) if=$(STAGE2_BIN) of=$(DISK_IMG) bs=512 seek=2 conv=notrunc 2>/dev/null
	@echo "Writing Simple Kernel starting at sector 18..."
	$(DD) if=$(SIMPLE_KERNEL_BIN) of=$(DISK_IMG) bs=512 seek=18 conv=notrunc 2>/dev/null
	@echo "Disk image created: $(DISK_IMG)"
	@echo ""
	@echo "File sizes:"
	@ls -la $(STAGE1_BIN) $(STAGE2_BIN) $(SIMPLE_KERNEL_BIN)
	@echo ""
	@echo "Verifying kernel placement:"
	@echo "Kernel should start at byte $$(echo '18 * 512' | bc) (sector 18)"

# Create disk image with example kernel
example-disk: $(STAGE1_BIN) $(STAGE2_BIN) $(EXAMPLE_KERNEL_BIN)
	@echo "Creating disk image with Example Kernel..."
	$(DD) if=/dev/zero of=$(DISK_IMG) bs=512 count=2880 2>/dev/null
	@echo "Writing Stage 1 to boot sector..."
	$(DD) if=$(STAGE1_BIN) of=$(DISK_IMG) bs=512 conv=notrunc 2>/dev/null
	@echo "Writing Stage 2 starting at sector 2..."
	$(DD) if=$(STAGE2_BIN) of=$(DISK_IMG) bs=512 seek=2 conv=notrunc 2>/dev/null
	@echo "Writing Example Kernel starting at sector 18..."
	$(DD) if=$(EXAMPLE_KERNEL_BIN) of=$(DISK_IMG) bs=512 seek=18 conv=notrunc 2>/dev/null
	@echo "Disk image created: $(DISK_IMG)"
	@echo ""
	@echo "File sizes:"
	@ls -la $(STAGE1_BIN) $(STAGE2_BIN) $(EXAMPLE_KERNEL_BIN)

# Test with QEMU
test: $(DISK_IMG)
	@echo "Starting QEMU..."
	$(QEMU) -fda $(DISK_IMG) -boot a

# Test with QEMU (no graphics)
test-nographic: $(DISK_IMG)
	@echo "Starting QEMU (no graphics)..."
	$(QEMU) -fda $(DISK_IMG) -boot a -nographic

# Build simple assembly-only Stage 2 for debugging
stage2_simple.bin: stage2_simple.asm
	@echo "Assembling simple Stage 2..."
	$(ASM) -f bin stage2_simple.asm -o stage2_simple.bin

# Create disk image with simple Stage 2
simple-disk: $(STAGE1_BIN) stage2_simple.bin $(SIMPLE_KERNEL_BIN)
	@echo "Creating disk image with Simple Stage 2..."
	$(DD) if=/dev/zero of=$(DISK_IMG) bs=512 count=2880 2>/dev/null
	@echo "Writing Stage 1 to boot sector..."
	$(DD) if=$(STAGE1_BIN) of=$(DISK_IMG) bs=512 conv=notrunc 2>/dev/null
	@echo "Writing Simple Stage 2 starting at sector 2..."
	$(DD) if=stage2_simple.bin of=$(DISK_IMG) bs=512 seek=2 conv=notrunc 2>/dev/null
	@echo "Writing Simple Kernel starting at sector 18..."
	$(DD) if=$(SIMPLE_KERNEL_BIN) of=$(DISK_IMG) bs=512 seek=18 conv=notrunc 2>/dev/null
	@echo "Simple disk image created: $(DISK_IMG)"

# Debug: Create a version with debug info
debug: $(DISK_IMG)
	@echo "Creating debug disk image with sector map..."
	@echo "Sector 0: Stage 1 (MBR)"
	@echo "Sectors 2-17: Stage 2"  
	@echo "Sectors 18-20: Kernel (3 sectors)"
	@echo ""
	@echo "Hex dump of kernel area (sectors 18-20):"
	@$(DD) if=$(DISK_IMG) bs=512 skip=18 count=3 2>/dev/null | hexdump -C | head -20

# Test kernel sizes
test-size: $(SIMPLE_KERNEL_BIN) $(EXAMPLE_KERNEL_BIN)
	@echo "Simple Kernel size check:"
	@SIZE=$$(stat -c%s $(SIMPLE_KERNEL_BIN)); \
	SECTORS=$$((($$SIZE + 511) / 512)); \
	echo "Simple Kernel size: $$SIZE bytes ($$SECTORS sectors)"; \
	if [ $$SECTORS -gt 8 ]; then \
		echo "WARNING: Simple Kernel is larger than 8 sectors!"; \
		echo "Update KERNEL_SIZE_SECTORS in stage2.c to $$SECTORS"; \
	else \
		echo "Simple Kernel fits in allocated space."; \
	fi
	@echo ""
	@echo "Example Kernel size check:"
	@SIZE=$$(stat -c%s $(EXAMPLE_KERNEL_BIN)); \
	SECTORS=$$((($$SIZE + 511) / 512)); \
	echo "Example Kernel size: $$SIZE bytes ($$SECTORS sectors)"; \
	if [ $$SECTORS -gt 8 ]; then \
		echo "WARNING: Example Kernel is larger than 8 sectors!"; \
		echo "Update KERNEL_SIZE_SECTORS in stage2.c to $$SECTORS"; \
	else \
		echo "Example Kernel fits in allocated space."; \
	fi

# Clean build artifacts
clean:
	@echo "Cleaning..."
	rm -f $(STAGE1_BIN) $(STAGE2_BIN) $(SIMPLE_KERNEL_BIN) $(EXAMPLE_KERNEL_BIN) $(DISK_IMG)
	rm -f *.o bootloader.bin
	@echo "Clean complete!"

# Install dependencies (Ubuntu/Debian)
install-deps:
	@echo "Installing dependencies..."
	sudo apt-get update
	sudo apt-get install -y nasm qemu-system-x86 bc

# Show disk image info
info: $(DISK_IMG)
	@echo "Disk image information:"
	@ls -la $(DISK_IMG)
	@echo ""
	@echo "Boot sector (first 32 bytes):"
	@hexdump -C $(DISK_IMG) | head -2
	@echo ""
	@echo "Stage 2 start (sector 2, first 32 bytes):"
	@$(DD) if=$(DISK_IMG) bs=512 skip=2 count=1 2>/dev/null | hexdump -C | head -2
	@echo ""
	@echo "Kernel start (sector 18, first 32 bytes):"
	@$(DD) if=$(DISK_IMG) bs=512 skip=18 count=1 2>/dev/null | hexdump -C | head -2

# Help
help:
	@echo "Available targets:"
	@echo "  all              - Build bootloader with Simple Kernel (default)"
	@echo "  example-disk     - Build bootloader with Example Kernel"
	@echo "  test             - Test bootloader with QEMU"
	@echo "  test-nographic   - Test bootloader with QEMU (no graphics)"
	@echo "  debug            - Show debug information about disk layout"
	@echo "  test-size        - Check if kernels fit in allocated sectors"
	@echo "  clean            - Remove build artifacts"
	@echo "  install-deps     - Install dependencies (Ubuntu/Debian)"
	@echo "  info             - Show disk image information"
	@echo "  help             - Show this help"

.PHONY: all example-disk test test-nographic debug test-size clean install-deps info help