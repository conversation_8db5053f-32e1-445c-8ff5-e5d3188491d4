; Assembly stub and BIOS interface for stage2.c
[BITS 16]

; External C function
extern stage2_main

; Entry point - called by stage1
global _start
_start:
    ; Simple approach: set up segments to match CS and call C code directly
    cli                  ; Disable interrupts during setup
    mov ax, cs           ; CS = 0x1000
    mov ds, ax           ; DS = CS (so data is accessible)
    mov es, ax           ; ES = CS
    mov ss, ax           ; SS = CS
    mov sp, 0xFFFE       ; Stack at top of segment
    sti                  ; Re-enable interrupts

    ; Debug: Print 'X' before calling C function
    mov ah, 0x0E
    mov al, 'X'
    int 0x10

    ; Debug: Simple marker
    mov ah, 0x0E
    mov al, '2'  ; 2 for Stage 2
    int 0x10

    ; Call C main function with boot drive parameter
    ; Use 16-bit calling convention since we're in 16-bit mode
    xor ax, ax       ; Clear AX
    mov al, dl       ; Put boot drive in AL
    push ax          ; Push 16-bit parameter
    call stage2_main
    add sp, 2        ; Clean up stack (16-bit)

    ; Success! Print final marker
    mov ah, 0x0E
    mov al, '!'
    int 0x10

    ; Immediate halt
    jmp halt_here

halt_here:
    ; Should never reach here
    cli
    hlt
    jmp halt_here

; BIOS interface functions for C code
global clear_screen
clear_screen:
    push bp
    mov bp, sp
    push ax

    mov ah, 0x00
    mov al, 0x03
    int 0x10

    pop ax
    pop bp
    ret

global print_string
print_string:
    push bp
    mov bp, sp
    push ax
    push si

    mov si, [bp+4]   ; Get string pointer parameter (16-bit calling convention)
.loop:
    lodsb
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    jmp .loop
.done:
    pop si
    pop ax
    pop bp
    ret

global print_hex_byte
print_hex_byte:
    push bp
    mov bp, sp
    push ax
    push cx

    mov al, [bp+4]   ; Get byte parameter
    mov cl, 4
    mov ah, al
    shr al, cl
    call print_hex_digit
    mov al, ah
    and al, 0x0F
    call print_hex_digit

    pop cx
    pop ax
    pop bp
    ret

print_hex_digit:
    and al, 0x0F
    cmp al, 9
    jbe .digit
    add al, 'A' - '0' - 10
.digit:
    add al, '0'
    mov ah, 0x0E
    int 0x10
    ret

print_hex_word:
    push ax
    mov al, ah
    call print_hex_byte_raw
    pop ax
    call print_hex_byte_raw
    ret

print_hex_byte_raw:
    push ax
    push cx
    mov cl, 4
    mov ah, al
    shr al, cl
    call print_hex_digit
    mov al, ah
    and al, 0x0F
    call print_hex_digit
    pop cx
    pop ax
    ret

global detect_memory
detect_memory:
    push bp
    mov bp, sp
    push ax

    mov ah, 0x88
    int 0x15
    jnc .success
    mov ax, 0
.success:
    ; Return non-zero for success
    test ax, ax
    jz .fail
    mov ax, 1
    jmp .done
.fail:
    mov ax, 0
.done:
    pop ax  ; This will be overwritten by return value
    pop bp
    ret

global detect_cpu
detect_cpu:
    push bp
    mov bp, sp
    push cx

    pushf
    pop ax
    mov cx, ax
    xor ax, 0x1000
    push ax
    popf
    pushf
    pop ax
    push cx
    popf
    cmp ax, cx
    je .cpu_8086
    mov ax, 1    ; 386+
    jmp .done
.cpu_8086:
    mov ax, 0    ; 8086
.done:
    pop cx
    pop bp
    ret

global check_lba_support
check_lba_support:
    push bp
    mov bp, sp
    push bx
    push cx
    push dx

    mov dl, [bp+4]   ; Get drive parameter
    mov ah, 0x41
    mov bx, 0x55AA
    int 0x13
    jc .no_lba
    cmp bx, 0xAA55
    jne .no_lba
    test cx, 1
    jz .no_lba
    mov ax, 1        ; LBA supported
    jmp .done
.no_lba:
    mov ax, 0        ; No LBA
.done:
    pop dx
    pop cx
    pop bx
    pop bp
    ret

global load_kernel_lba
load_kernel_lba:
    push bp
    mov bp, sp
    push si
    push dx

    ; Parameters: drive, start_sector, sectors, segment
    mov byte [dap], 16
    mov byte [dap+1], 0
    mov ax, [bp+8]   ; sectors
    mov [dap+2], ax
    mov word [dap+4], 0x0000
    mov ax, [bp+10]  ; segment
    mov [dap+6], ax
    mov ax, [bp+6]   ; start_sector
    mov [dap+8], ax
    mov word [dap+10], 0
    mov word [dap+12], 0

    mov si, dap
    mov ah, 0x42
    mov dl, [bp+4]   ; drive
    int 0x13
    jc .error
    mov ax, 1        ; Success
    jmp .done
.error:
    mov ax, 0        ; Failure
.done:
    pop dx
    pop si
    pop bp
    ret

global load_kernel_chs
load_kernel_chs:
    push bp
    mov bp, sp
    push bx
    push dx

    ; Reset disk first
    mov ah, 0x00
    mov dl, [bp+4]   ; drive
    int 0x13
    jc .error

    ; Set up destination
    mov ax, [bp+10]  ; segment
    mov es, ax
    xor bx, bx

    ; Load sectors
    mov ah, 0x02
    mov al, [bp+8]   ; sectors
    mov ch, 0        ; cylinder
    mov cl, [bp+6]   ; start sector
    mov dh, 0        ; head
    mov dl, [bp+4]   ; drive
    int 0x13
    jc .error
    mov ax, 1        ; Success
    jmp .done
.error:
    mov ax, 0        ; Failure
.done:
    pop dx
    pop bx
    pop bp
    ret

global get_keystroke
get_keystroke:
    push bp
    mov bp, sp

    mov ah, 0x00
    int 0x16
    ; AL contains the character

    pop bp
    ret

global jump_to_kernel
jump_to_kernel:
    push bp
    mov bp, sp

    cli

    ; Set up segment registers
    mov ax, [bp+4]   ; segment
    mov ds, ax
    mov es, ax

    ; Pass boot drive
    mov dl, [bp+6]   ; boot_drive

    ; Jump to kernel using far jump
    mov ax, [bp+4]   ; segment
    push ax
    push word 0x0000 ; offset
    retf             ; Far return acts as far jump

; Data section
dap: times 16 db 0
