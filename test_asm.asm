[BITS 16]

extern test_function

global _start
_start:
    ; Set up segments
    mov ax, cs
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0xFFFE
    
    ; Print 'A' before C call
    mov ah, 0x0E
    mov al, 'A'
    int 0x10
    
    ; Call C function
    call test_function
    
    ; Print 'B' after C call
    mov ah, 0x0E
    mov al, 'B'
    int 0x10
    
    ; Halt
    cli
    hlt
    jmp $
