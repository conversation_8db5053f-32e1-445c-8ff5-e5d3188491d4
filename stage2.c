/*
 * Fe<PERSON>y Bootloader - Stage 2 (C Version)
 * Converted from stage2.asm to C while maintaining the same functionality
 */

// BIOS interrupt wrappers and low-level functions
void clear_screen(void);
void print_string(const char* str);
void print_hex_byte(unsigned char value);
int detect_memory(void);
int detect_cpu(void);
int load_kernel_lba(unsigned char drive, unsigned int start_sector, unsigned char sectors, unsigned int segment);
int load_kernel_chs(unsigned char drive, unsigned int start_sector, unsigned char sectors, unsigned int segment);
int check_lba_support(unsigned char drive);
char get_keystroke(void);
void reboot_system(void);
void jump_to_kernel(unsigned int segment, unsigned char boot_drive);

// Constants
#define KERNEL_LOAD_SEGMENT  0x8000
#define KERNEL_SIZE_SECTORS  8
#define KERNEL_LBA_START     18

// Global variables
static unsigned char boot_drive = 0;

// Function prototypes
void stage2_main(unsigned char drive);
void display_header(void);
void detect_system(void);
void display_boot_menu(void);
void handle_user_input(void);
void load_kernel(void);
void prepare_kernel_jump(void);
void critical_error(const char* message);

// Inline assembly helper for printing a character
static void print_char(char c) {
    __asm__ volatile (
        "mov $0x0E, %%ah\n\t"
        "mov %0, %%al\n\t"
        "int $0x10"
        :
        : "r" (c)
        : "ax"
    );
}

// Inline assembly helper for printing a string
static void print_string_inline(const char* str) {
    while (*str) {
        print_char(*str);
        str++;
    }
}

// Helper function to print a character using inline assembly
static void print_char_inline(char c) {
    __asm__ volatile (
        "mov $0x0E, %%ah\n\t"
        "mov %0, %%al\n\t"
        "int $0x10"
        :
        : "r" (c)
        : "ax"
    );
}

// Helper function to print a string using inline assembly
static void print_string_safe(const char* str) {
    while (*str) {
        print_char_inline(*str);
        str++;
    }
}

// Entry point called from assembly stub
void stage2_main(unsigned char drive) {
    boot_drive = drive;

    // Clear screen using inline assembly
    __asm__ volatile (
        "mov $0x00, %%ah\n\t"
        "mov $0x03, %%al\n\t"
        "int $0x10"
        :
        :
        : "ax"
    );

    // Print header
    print_string_safe("Stage 2 Bootloader (C Version)\r\n");
    print_string_safe("==============================\r\n\r\n");

    // Simple boot menu
    print_string_safe("Boot Menu:\r\n");
    print_string_safe("[1] Continue to kernel\r\n");
    print_string_safe("[ESC] Halt\r\n\r\n");
    print_string_safe("Select option: ");

    // For now, just continue automatically
    print_string_safe("1\r\n\r\n");
    print_string_safe("Loading kernel... ");

    // Load kernel using inline assembly
    int load_result = 0;
    __asm__ volatile (
        // Reset disk first
        "mov $0x00, %%ah\n\t"
        "mov %2, %%dl\n\t"
        "int $0x13\n\t"
        "jc 1f\n\t"

        // Load kernel sectors
        "mov $0x02, %%ah\n\t"      // Read sectors
        "mov $8, %%al\n\t"         // Number of sectors
        "mov $0, %%ch\n\t"         // Cylinder 0
        "mov $18, %%cl\n\t"        // Starting sector 18
        "mov $0, %%dh\n\t"         // Head 0
        "mov %2, %%dl\n\t"         // Drive
        "mov $0x8000, %%bx\n\t"    // Destination segment
        "mov %%bx, %%es\n\t"
        "xor %%bx, %%bx\n\t"       // Destination offset
        "int $0x13\n\t"
        "jc 1f\n\t"

        // Success
        "mov $1, %0\n\t"
        "jmp 2f\n\t"

        // Error
        "1:\n\t"
        "mov $0, %0\n\t"
        "2:"
        : "=m" (load_result)
        : "m" (load_result), "m" (boot_drive)
        : "ax", "bx", "cx", "dx"
    );

    if (load_result) {
        print_string_safe("OK!\r\n");
        print_string_safe("Jumping to kernel...\r\n");

        // Jump to kernel using inline assembly
        __asm__ volatile (
            "cli\n\t"
            "mov %0, %%dl\n\t"         // Pass boot drive
            "ljmp $0x8000, $0x0000"   // Far jump to kernel
            :
            : "m" (boot_drive)
            : "dx"
        );
    } else {
        print_string_safe("FAILED!\r\n");
        print_string_safe("Disk read error. System halted.\r\n");

        // Halt system
        __asm__ volatile ("cli; hlt");
    }

    return;
}

void display_header(void) {
    print_string("DEBUG Stage 2 Bootloader v1.1 (C Version)\r\n");
    print_string("==========================================\r\n\r\n");
}

void detect_system(void) {
    print_string("Detecting Memory... ");
    if (detect_memory()) {
        print_string("OK\r\n");
    } else {
        print_string("FAILED\r\n");
    }
    
    print_string("Detecting CPU... ");
    if (detect_cpu()) {
        print_string("386+ Compatible\r\n");
    } else {
        print_string("8086/8088\r\n");
    }
}

void display_boot_menu(void) {
    print_string("\r\nBoot Menu:\r\n");
    print_string("---------\r\n");
    print_string("[1] Boot DEBUG Kernel\r\n");
}

void handle_user_input(void) {
    char key;
    
    print_string("\r\nSelect option (1) or ESC to halt: ");
    
    while (1) {
        key = get_keystroke();
        
        if (key == '1') {
            print_string("1\r\n\r\n");
            return;
        } else if (key == 27) { // ESC
            critical_error("User requested halt");
        }
    }
}

void load_kernel(void) {
    int result;
    
    print_string("Loading kernel... ");
    
    // Try LBA first
    if (check_lba_support(boot_drive)) {
        print_string("(LBA) ");
        result = load_kernel_lba(boot_drive, KERNEL_LBA_START, KERNEL_SIZE_SECTORS, KERNEL_LOAD_SEGMENT);
    } else {
        print_string("!!! CHS Fallback !!!\r\n");
        print_string("(CHS) ");
        result = load_kernel_chs(boot_drive, KERNEL_LBA_START, KERNEL_SIZE_SECTORS, KERNEL_LOAD_SEGMENT);
    }
    
    if (result) {
        print_string("OK!\r\n");
    } else {
        critical_error("Failed to load kernel!");
    }
}

void prepare_kernel_jump(void) {
    unsigned char* kernel_ptr = (unsigned char*)(KERNEL_LOAD_SEGMENT << 4);
    
    print_string("Preparing kernel jump... ");
    
    // Show what we loaded (debug info)
    print_string("Loaded kernel bytes: ");
    print_hex_byte(kernel_ptr[0]);
    print_string(" ");
    print_hex_byte(kernel_ptr[1]);
    print_string(" ");
    print_hex_byte(kernel_ptr[2]);
    print_string(" ");
    print_hex_byte(kernel_ptr[3]);
    print_string("\r\n");
    
    print_string("Skipping verification...\r\n");
}

void critical_error(const char* message) {
    print_string("CRITICAL ERROR: ");
    print_string(message);
    print_string("\r\nSystem halted.\r\n");
    
    // Halt the system
    while (1) {
        __asm__ volatile ("cli; hlt");
    }
}
