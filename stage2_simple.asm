; Simple Assembly-only Stage 2 for debugging
[BITS 16]
[ORG 0x0000]

; Entry point - called by stage1
global _start
_start:
    ; Immediate debug - print 'S' to show we reached Stage 2
    mov ah, 0x0E
    mov al, 'S'
    int 0x10

    ; Set up segments - CS is 0x1000, set others to match
    cli                  ; Disable interrupts during setup
    mov ax, cs           ; CS = 0x1000
    mov ds, ax           ; DS = CS (so data is accessible)
    mov es, ax           ; ES = CS
    mov ss, ax           ; SS = CS
    mov sp, 0xFFFE       ; Stack at top of segment
    sti                  ; Re-enable interrupts

    ; Another debug marker
    mov ah, 0x0E
    mov al, '2'
    int 0x10

    ; Clear screen
    mov ah, 0x00
    mov al, 0x03
    int 0x10

    ; Print Stage 2 message
    mov si, msg_stage2
    call print_string

    ; Print boot drive info
    mov si, msg_drive
    call print_string
    mov al, dl
    call print_hex_byte
    mov si, msg_newline
    call print_string

    ; Load kernel
    mov si, msg_loading_kernel
    call print_string
    
    call load_kernel
    
    mov si, msg_kernel_loaded
    call print_string

    ; Jump to kernel
    mov si, msg_jumping_kernel
    call print_string
    
    ; Jump to kernel at 0x8000:0x0000
    mov dl, [boot_drive]  ; Pass boot drive
    jmp 0x8000:0x0000

; Load kernel function
load_kernel:
    ; Save boot drive
    mov [boot_drive], dl
    
    ; Reset disk
    mov ah, 0x00
    mov dl, [boot_drive]
    int 0x13
    jc .error

    ; Load kernel using CHS
    mov ah, 0x02         ; Read sectors
    mov al, 8            ; Number of sectors
    mov ch, 0            ; Cylinder 0
    mov cl, 18           ; Starting sector 18
    mov dh, 0            ; Head 0
    mov dl, [boot_drive] ; Drive
    mov bx, 0x8000       ; Destination segment
    mov es, bx
    xor bx, bx           ; Destination offset
    int 0x13
    jc .error
    ret

.error:
    mov si, msg_error
    call print_string
    jmp halt

; Print string function
print_string:
    push ax
.loop:
    lodsb
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    jmp .loop
.done:
    pop ax
    ret

; Print hex byte function
print_hex_byte:
    push ax
    push cx
    mov cl, 4
    mov ah, al
    shr al, cl
    call print_hex_digit
    mov al, ah
    and al, 0x0F
    call print_hex_digit
    pop cx
    pop ax
    ret

print_hex_digit:
    and al, 0x0F
    cmp al, 9
    jbe .digit
    add al, 'A' - '0' - 10
.digit:
    add al, '0'
    mov ah, 0x0E
    int 0x10
    ret

halt:
    cli
    hlt
    jmp halt

; Data
msg_stage2 db 'Stage 2 Bootloader (ASM)', 13, 10, 0
msg_drive db 'Boot drive: 0x', 0
msg_newline db 13, 10, 0
msg_loading_kernel db 'Loading kernel...', 0
msg_kernel_loaded db ' OK', 13, 10, 0
msg_jumping_kernel db 'Jumping to kernel...', 13, 10, 0
msg_error db ' ERROR!', 13, 10, 'Disk read failed.', 13, 10, 0

boot_drive db 0

; Pad to make sure we have enough space
times 2048-($-$$) db 0
