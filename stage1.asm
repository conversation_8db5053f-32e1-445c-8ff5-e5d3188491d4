; Femboy Bootloader - Stage 1
; This is the first stage, residing in the MBR. Its only job is to load
; the second, more powerful stage of the bootloader from the disk.

[BITS 16]
[ORG 0x7C00]

; --- Constants ---
STAGE2_LOAD_SEGMENT equ 0x1000  ; Memory segment to load Stage 2 into
STAGE2_START_SECTOR equ 2       ; The sector where Stage 2 begins
STAGE2_SECTORS      equ 16      ; Number of sectors Stage 2 occupies (16 * 512 = 8KB)

; --- Entry Point ---
start:
    ; The BIOS passes the boot drive number in 'dl'. We must save it.
    mov [boot_drive], dl

    ; --- Segment Initialization ---
    ; Set up a flat memory model (DS=ES=SS=0) and a proper stack.
    cli             ; Disable interrupts during stack setup
    xor ax, ax
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0x7000  ; Stack grows downwards from here
    sti             ; Re-enable interrupts

    ; --- User Feedback ---
    ; Clear the screen and print a loading message.
    mov ax, 0x0003  ; Set 80x25 text mode (clears screen)
    int 0x10

    ; Debug: Show Stage 1 CS
    mov ah, 0x0E
    mov al, '1'
    int 0x10
    mov ah, 0x0E
    mov al, '['
    int 0x10
    mov ax, cs
    call print_hex_word
    mov ah, 0x0E
    mov al, ']'
    int 0x10

    mov si, msg_loading
    call print_string

    ; --- Load Stage 2 ---
    ; Attempt to load the second stage from the disk.
    call load_stage2

    ; --- Jump to Stage 2 ---
    ; If loading was successful, pass the boot drive and jump to Stage 2.
    mov si, msg_jumping
    call print_string

    ; Debug: Simple marker
    mov ah, 0x0E
    mov al, 'J'  ; J for Jump
    int 0x10

    ; Add delay to see messages
    mov cx, 0xFFFF
    delay_loop:
        nop
        loop delay_loop

    ; Debug: Show where we're jumping
    mov ah, 0x0E
    mov al, '>'
    int 0x10
    mov ax, STAGE2_LOAD_SEGMENT
    call print_hex_word
    mov ah, 0x0E
    mov al, '<'
    int 0x10

    mov dl, [boot_drive] ; Pass boot drive to Stage 2
    jmp STAGE2_LOAD_SEGMENT:0x0000  ; Jump to Stage 2

; --- load_stage2 Function ---
; Reads Stage 2 from the disk into memory using BIOS services.
load_stage2:
    ; First, reset the disk system to ensure it's in a good state.
    mov ah, 0x00
    mov dl, [boot_drive]
    int 0x13
    jc .error ; If reset fails, we can't proceed.

    ; Use BIOS 'int 0x13' to read sectors from the disk.
    mov ah, 0x02                ; AH=0x02 -> Read Sectors
    mov al, 16                  ; Number of sectors to read (literal)
    mov ch, 0                   ; Cylinder 0
    mov cl, 2                   ; Starting sector (literal)
    mov dh, 0                   ; Head 0
    mov dl, [boot_drive]        ; Boot drive
    mov bx, STAGE2_LOAD_SEGMENT ; Destination segment
    mov es, bx                  ; Set ES to the destination segment
    xor bx, bx                  ; Destination offset 0
    int 0x13
    jc .error                   ; If Carry Flag is set, an error occurred.
    ret

.error:
    ; If the disk read fails, print an error and halt.
    mov si, msg_error
    call print_string
halt:
    hlt
    jmp halt

; --- print_string Function ---
; Prints a null-terminated string to the screen via BIOS teletype.
; Input: SI = address of the string
print_string:
    push ax         ; Save the AX register
.loop:
    lodsb           ; Load byte from [SI] into AL, increment SI
    test al, al     ; Is the byte null?
    jz .done        ; If so, we are done.
    mov ah, 0x0E    ; AH=0x0E -> Teletype output
    int 0x10
    jmp .loop
.done:
    pop ax          ; Restore the AX register
    ret

; --- print_hex_word Function ---
; Prints a 16-bit value in hexadecimal
; Input: AX = value to print
print_hex_word:
    push ax
    mov al, ah
    call print_hex_byte
    pop ax
    call print_hex_byte
    ret

print_hex_byte:
    push ax
    push cx
    mov cl, 4
    mov ah, al
    shr al, cl
    call print_hex_digit
    mov al, ah
    and al, 0x0F
    call print_hex_digit
    pop cx
    pop ax
    ret

print_hex_digit:
    and al, 0x0F
    cmp al, 9
    jbe .digit
    add al, 'A' - '0' - 10
.digit:
    add al, '0'
    mov ah, 0x0E
    int 0x10
    ret

; --- Data ---
msg_loading db 'Femboot Stage 1 | Loading Stage 2...', 13, 10, 0
msg_jumping db ' OK', 13, 10, 0
msg_error   db 'ERR!', 13, 10, 'Failed to load Stage 2. System halted.', 0

boot_drive  db 0

; --- Bootloader Signature ---
; Pad the file to 510 bytes and add the magic number 0xAA55.
times 510-($-$$) db 0
dw 0xAA55